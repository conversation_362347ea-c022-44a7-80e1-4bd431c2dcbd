from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Type
from pathlib import Path
from ..models.evaluation_models import (
    EvaluationDimension, MetricResult, DimensionResult, EvaluationLevel
)
from ..models.corpus_model import FTCorpus


class BaseMetric(ABC):
    def __init__(self, name: str, weight: float = 1.0, enabled: bool = True):
        self.name = name
        self.weight = weight
        self.enabled = enabled
        self.config = {}

    def set_config(self, config: Dict[str, Any]):
        self.config = config

    def create_result(self, score: float, level: EvaluationLevel,
                     message: str, dimension: EvaluationDimension,
                     details: Dict[str, Any] = None,
                     suggestions: List[str] = None) -> MetricResult:
        return MetricResult(
            metric_name=self.name,
            dimension=dimension,
            score=score,
            weight=self.weight,
            level=level,
            message=message,
            details=details or {},
            suggestions=suggestions or []
        )


class CorpusMetric(BaseMetric):
    """单语料指标基类"""

    @abstractmethod
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        """评估单个语料"""
        pass

    def collect_info(self, corpus: FTCorpus) -> Dict[str, Any]:
        """收集语料集指标需要的信息"""
        return {}

    def get_collection_info_keys(self) -> List[str]:
        """声明此指标会收集哪些信息键"""
        return []


class CollectionMetric(BaseMetric):
    """语料集指标基类"""

    @abstractmethod
    def calculate(self, collected_info: Dict[str, List[Any]]) -> MetricResult:
        """基于收集的信息计算语料集指标"""
        pass

    @abstractmethod
    def required_info_keys(self) -> List[str]:
        """声明需要哪些信息键"""
        pass


class InfoCollector:
    """信息收集器"""

    def __init__(self):
        self.collected_info: Dict[str, List[Any]] = {}
        self.corpus_results: List[Dict[str, MetricResult]] = []

    def collect_from_corpus(self, corpus: FTCorpus, corpus_metrics: List[CorpusMetric]) -> Dict[str, MetricResult]:
        """评估单语料并收集信息"""
        corpus_result = {}

        for metric in corpus_metrics:
            if not metric.enabled:
                continue

            try:
                # 评估单语料指标
                result = metric.evaluate(corpus)
                corpus_result[metric.name] = result

                # 收集语料集指标需要的信息
                info = metric.collect_info(corpus)
                for key, value in info.items():
                    if key not in self.collected_info:
                        self.collected_info[key] = []
                    self.collected_info[key].append(value)

            except Exception as e:
                # 处理评估错误
                error_result = MetricResult(
                    metric_name=metric.name,
                    dimension=EvaluationDimension.COMPLETENESS,  # 临时值，会被具体维度覆盖
                    score=0,
                    weight=metric.weight,
                    level=EvaluationLevel.CRITICAL,
                    message=f"Metric evaluation failed: {str(e)}",
                    details={"error": str(e)}
                )
                corpus_result[metric.name] = error_result

        self.corpus_results.append(corpus_result)
        return corpus_result

    def calculate_collection_metrics(self, collection_metrics: List[CollectionMetric]) -> Dict[str, MetricResult]:
        """计算语料集指标"""
        collection_results = {}

        for metric in collection_metrics:
            if not metric.enabled:
                continue

            try:
                # 检查所需信息是否已收集
                required_keys = metric.required_info_keys()
                available_info = {key: self.collected_info[key]
                                for key in required_keys
                                if key in self.collected_info}

                # 计算语料集指标
                result = metric.calculate(available_info)
                collection_results[metric.name] = result

            except Exception as e:
                # 处理计算错误
                error_result = MetricResult(
                    metric_name=metric.name,
                    dimension=EvaluationDimension.COMPLETENESS,  # 临时值，会被具体维度覆盖
                    score=0,
                    weight=metric.weight,
                    level=EvaluationLevel.CRITICAL,
                    message=f"Collection metric calculation failed: {str(e)}",
                    details={"error": str(e)}
                )
                collection_results[metric.name] = error_result

        return collection_results

    def clear(self):
        """清空收集的信息"""
        self.collected_info.clear()
        self.corpus_results.clear()


class UnifiedDimension:
    """统一的维度管理器，支持单语料指标和语料集指标"""

    def __init__(self, dimension: EvaluationDimension):
        self.dimension = dimension
        self.corpus_metrics: List[CorpusMetric] = []
        self.collection_metrics: List[CollectionMetric] = []
        self.info_collector = InfoCollector()
        self.config = {}
    
    def register_corpus_metric(self, metric: CorpusMetric):
        """注册单语料指标"""
        self.corpus_metrics.append(metric)
        if metric.name in self.config.get('corpus_metrics', {}):
            metric.set_config(self.config['corpus_metrics'][metric.name])

    def register_collection_metric(self, metric: CollectionMetric):
        """注册语料集指标"""
        self.collection_metrics.append(metric)
        if metric.name in self.config.get('collection_metrics', {}):
            metric.set_config(self.config['collection_metrics'][metric.name])

    def set_config(self, config: Dict[str, Any]):
        """设置配置"""
        self.config = config

        # 配置单语料指标
        for metric in self.corpus_metrics:
            if metric.name in config.get('corpus_metrics', {}):
                metric.set_config(config['corpus_metrics'][metric.name])

        # 配置语料集指标
        for metric in self.collection_metrics:
            if metric.name in config.get('collection_metrics', {}):
                metric.set_config(config['collection_metrics'][metric.name])

    def evaluate_corpus(self, corpus: FTCorpus) -> Dict[str, MetricResult]:
        """评估单语料并收集信息"""
        return self.info_collector.collect_from_corpus(corpus, self.corpus_metrics)

    def calculate_collection_metrics(self) -> Dict[str, MetricResult]:
        """计算语料集指标"""
        return self.info_collector.calculate_collection_metrics(self.collection_metrics)

    def get_all_results(self) -> Dict[str, Any]:
        """获取所有结果"""
        return {
            "corpus_results": self.info_collector.corpus_results,
            "collection_results": self.calculate_collection_metrics()
        }

    def clear_collection(self):
        """清空收集的信息"""
        self.info_collector.clear()

    def get_corpus_count(self) -> int:
        """获取已处理的语料数量"""
        return len(self.info_collector.corpus_results)

    def get_enabled_corpus_metrics(self) -> List[str]:
        """获取启用的单语料指标名称"""
        return [metric.name for metric in self.corpus_metrics if metric.enabled]

    def get_enabled_collection_metrics(self) -> List[str]:
        """获取启用的语料集指标名称"""
        return [metric.name for metric in self.collection_metrics if metric.enabled]

    def enable_corpus_metric(self, metric_name: str):
        """启用单语料指标"""
        for metric in self.corpus_metrics:
            if metric.name == metric_name:
                metric.enabled = True
                break

    def disable_corpus_metric(self, metric_name: str):
        """禁用单语料指标"""
        for metric in self.corpus_metrics:
            if metric.name == metric_name:
                metric.enabled = False
                break

    def enable_collection_metric(self, metric_name: str):
        """启用语料集指标"""
        for metric in self.collection_metrics:
            if metric.name == metric_name:
                metric.enabled = True
                break

    def disable_collection_metric(self, metric_name: str):
        """禁用语料集指标"""
        for metric in self.collection_metrics:
            if metric.name == metric_name:
                metric.enabled = False
                break


# 保持向后兼容的BaseDimension类（已废弃，建议使用UnifiedDimension）
class BaseDimension(ABC):
    def __init__(self, dimension: EvaluationDimension):
        self.dimension = dimension
        self.metrics: Dict[str, CorpusMetric] = {}  # 只支持单语料指标
        self.config = {}

    def register_metric(self, metric: CorpusMetric):
        self.metrics[metric.name] = metric
        if metric.name in self.config.get('metrics', {}):
            metric.set_config(self.config['metrics'][metric.name])

    def set_config(self, config: Dict[str, Any]):
        self.config = config
        for metric_name, metric_config in config.get('metrics', {}).items():
            if metric_name in self.metrics:
                self.metrics[metric_name].set_config(metric_config)

    def evaluate(self, corpus: FTCorpus) -> DimensionResult:
        metric_results = []
        total_weighted_score = 0
        total_weight = 0
        enabled_metrics = []

        for metric in self.metrics.values():
            if not metric.enabled:
                continue

            enabled_metrics.append(metric.name)
            try:
                result = metric.evaluate(corpus)
                metric_results.append(result)
                total_weighted_score += result.score * metric.weight
                total_weight += metric.weight
            except Exception as e:
                error_result = MetricResult(
                    metric_name=metric.name,
                    dimension=self.dimension,
                    score=0,
                    weight=metric.weight,
                    level=EvaluationLevel.CRITICAL,
                    message=f"Metric evaluation failed: {str(e)}",
                    details={"error": str(e)}
                )
                metric_results.append(error_result)

        overall_score = total_weighted_score / total_weight if total_weight > 0 else 0
        weighted_score = total_weighted_score

        dimension_result = DimensionResult(
            dimension=self.dimension,
            overall_score=overall_score,
            weighted_score=weighted_score,
            metric_results=metric_results,
            enabled_metrics=enabled_metrics
        )

        return dimension_result


class DimensionManager:
    def __init__(self):
        self.dimensions: Dict[EvaluationDimension, BaseDimension] = {}
        self.config = {}
    
    def register_dimension(self, dimension: BaseDimension):
        self.dimensions[dimension.dimension] = dimension
        if dimension.dimension.value in self.config:
            dimension.set_config(self.config[dimension.dimension.value])
    
    def unregister_dimension(self, dimension_type: EvaluationDimension):
        if dimension_type in self.dimensions:
            del self.dimensions[dimension_type]
    
    def register_metric(self, dimension_type: EvaluationDimension, metric: BaseMetric):
        if dimension_type in self.dimensions:
            self.dimensions[dimension_type].register_metric(metric)
    
    def set_config(self, config: Dict[str, Any]):
        self.config = config
        for dimension_name, dimension_config in config.items():
            dimension_enum = None
            try:
                dimension_enum = EvaluationDimension(dimension_name)
            except ValueError:
                continue
                
            if dimension_enum in self.dimensions:
                self.dimensions[dimension_enum].set_config(dimension_config)
    
    def evaluate_all_dimensions(self, corpus: FTCorpus) -> Dict[EvaluationDimension, DimensionResult]:
        results = {}
        for dimension_type, dimension in self.dimensions.items():
            results[dimension_type] = dimension.evaluate(corpus)
        return results
    
    def evaluate_dimension(self, corpus: FTCorpus, 
                          dimension_type: EvaluationDimension) -> Optional[DimensionResult]:
        if dimension_type in self.dimensions:
            return self.dimensions[dimension_type].evaluate(corpus)
        return None
    
    def get_enabled_metrics(self, dimension_type: EvaluationDimension) -> List[str]:
        if dimension_type in self.dimensions:
            return [name for name, metric in self.dimensions[dimension_type].metrics.items() 
                   if metric.enabled]
        return []
    
    def enable_metric(self, dimension_type: EvaluationDimension, metric_name: str):
        if (dimension_type in self.dimensions and 
            metric_name in self.dimensions[dimension_type].metrics):
            self.dimensions[dimension_type].metrics[metric_name].enabled = True
    
    def disable_metric(self, dimension_type: EvaluationDimension, metric_name: str):
        if (dimension_type in self.dimensions and 
            metric_name in self.dimensions[dimension_type].metrics):
            self.dimensions[dimension_type].metrics[metric_name].enabled = False


class MetricRegistry:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.metrics = {}
        return cls._instance
    
    def register_metric_class(self, dimension: EvaluationDimension, 
                            metric_name: str, metric_class: Type[BaseMetric]):
        key = f"{dimension.value}.{metric_name}"
        self.metrics[key] = metric_class
    
    def create_metric(self, dimension: EvaluationDimension, 
                     metric_name: str, **kwargs) -> Optional[BaseMetric]:
        key = f"{dimension.value}.{metric_name}"
        if key in self.metrics:
            return self.metrics[key](**kwargs)
        return None
    
    def get_available_metrics(self, dimension: EvaluationDimension) -> List[str]:
        prefix = f"{dimension.value}."
        return [key[len(prefix):] for key in self.metrics.keys() if key.startswith(prefix)]
    
    def get_all_available_metrics(self) -> Dict[str, List[str]]:
        result = {}
        for dimension in EvaluationDimension:
            result[dimension.value] = self.get_available_metrics(dimension)
        return result


class UnifiedEvaluator:
    """统一的评估器，支持单语料指标和语料集指标"""

    def __init__(self):
        self.dimensions: Dict[EvaluationDimension, UnifiedDimension] = {}
        self.config = {}

    def register_dimension(self, dimension: UnifiedDimension):
        """注册维度"""
        self.dimensions[dimension.dimension] = dimension
        if dimension.dimension.value in self.config:
            dimension.set_config(self.config[dimension.dimension.value])

    def set_config(self, config: Dict[str, Any]):
        """设置配置"""
        self.config = config
        for dimension_name, dimension_config in config.items():
            try:
                dimension_enum = EvaluationDimension(dimension_name)
                if dimension_enum in self.dimensions:
                    self.dimensions[dimension_enum].set_config(dimension_config)
            except ValueError:
                continue

    def evaluate_corpus_collection(self, corpus_files: List[Path], parser) -> Dict[str, Any]:
        for dimension in self.dimensions.values():
            dimension.clear_collection()

        corpus_results = []
        failed_files = []
        processed_count = 0

        print(f"📝 开始处理 {len(corpus_files)} 个语料文件")

        for i, file_path in enumerate(corpus_files, 1):
            try:
                corpus = parser.parse_file(file_path)

                corpus_result = {}
                for dim_type, dimension in self.dimensions.items():
                    corpus_result[dim_type.value] = dimension.evaluate_corpus(corpus)

                corpus_results.append({
                    'file_path': str(corpus.file_path),
                    'corpus_id': corpus.rdc_info.rdc_id,
                    'results': corpus_result
                })

                processed_count += 1

                if i % 10 == 0 or i == len(corpus_files):
                    print(f"📊 已处理 {i}/{len(corpus_files)} 个文件")

            except Exception as e:
                failed_files.append((file_path, str(e)))
                print(f"⚠️  解析失败: {file_path.name} - {e}")

        if processed_count == 0:
            raise ValueError("没有成功处理的语料文件")

        print(f"✅ 成功处理 {processed_count} 个语料文件")
        if failed_files:
            print(f"⚠️  处理失败 {len(failed_files)} 个文件")

        print("📊 计算语料集统计指标...")
        collection_results = {}
        for dim_type, dimension in self.dimensions.items():
            collection_results[dim_type.value] = dimension.calculate_collection_metrics()

        return {
            'corpus_results': corpus_results,
            'collection_results': collection_results,
            'summary': {
                'total_corpus_count': processed_count,
                'total_failed_count': len(failed_files),
                'dimensions_evaluated': list(self.dimensions.keys()),
                'failed_files': [(str(path), error) for path, error in failed_files]
            }
        }