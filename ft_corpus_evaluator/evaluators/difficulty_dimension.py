import re
from typing import List, Dict, Any, Set
from .dimension_framework import BaseDimension, CorpusMetric, CollectionMetric, UnifiedDimension
from ..models.evaluation_models import EvaluationDimension, EvaluationLevel, MetricResult
from ..models.corpus_model import FTCorpus


class TechnicalComplexityMetric(CorpusMetric):
    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("technical_complexity", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        complexity_score = 0
        complexity_factors = []
        
        technical_keywords = self.config.get('technical_keywords', {
            'high': ['并发', '多线程', '分布式', '集群', '负载均衡', '缓存', '数据库事务', '异步', '微服务'],
            'medium': ['接口', 'API', '配置', '网络', '协议', '算法', '数据结构', '性能'],
            'low': ['基本', '简单', '单一', '直接', '基础']
        })
        
        all_text = f"{corpus.test_info.test_title} {' '.join(corpus.test_info.tc_steps)}"
        
        high_count = sum(1 for keyword in technical_keywords['high'] if keyword in all_text)
        medium_count = sum(1 for keyword in technical_keywords['medium'] if keyword in all_text)
        low_count = sum(1 for keyword in technical_keywords['low'] if keyword in all_text)
        
        complexity_score += high_count * 15
        complexity_score += medium_count * 8
        complexity_score += low_count * 3
        
        if high_count > 0:
            complexity_factors.append(f"High complexity keywords: {high_count}")
        if medium_count > 0:
            complexity_factors.append(f"Medium complexity keywords: {medium_count}")
        
        code_complexity = self._analyze_code_complexity(corpus.code_snippets)
        complexity_score += code_complexity
        if code_complexity > 20:
            complexity_factors.append("Complex code patterns detected")
        
        steps_complexity = self._analyze_steps_complexity(corpus.test_info.tc_steps)
        complexity_score += steps_complexity
        if steps_complexity > 15:
            complexity_factors.append("Complex test procedures")
        
        complexity_score = min(100, complexity_score)
        
        if complexity_score >= 70:
            level = EvaluationLevel.INFO
            message = "High technical complexity"
            difficulty_level = "High"
        elif complexity_score >= 40:
            level = EvaluationLevel.INFO
            message = "Medium technical complexity"
            difficulty_level = "Medium"
        else:
            level = EvaluationLevel.INFO
            message = "Low technical complexity"
            difficulty_level = "Low"
        
        return self.create_result(
            score=complexity_score,
            level=level,
            message=message,
            dimension=EvaluationDimension.DIFFICULTY,
            details={
                "difficulty_level": difficulty_level,
                "complexity_factors": complexity_factors,
                "keyword_counts": {
                    "high": high_count,
                    "medium": medium_count,
                    "low": low_count
                },
                "code_complexity": code_complexity,
                "steps_complexity": steps_complexity
            },
            suggestions=[]
        )

    def collect_info(self, corpus: FTCorpus) -> Dict[str, Any]:
        """收集技术复杂度信息供语料集指标使用"""
        # 重用evaluate方法中的逻辑
        technical_keywords = self.config.get('technical_keywords', {
            'high': ['并发', '多线程', '分布式', '集群', '负载均衡', '缓存', '数据库事务', '异步', '微服务'],
            'medium': ['接口', 'API', '配置', '网络', '协议', '算法', '数据结构', '性能'],
            'low': ['基本', '简单', '单一', '直接', '基础']
        })

        all_text = f"{corpus.test_info.test_title} {' '.join(corpus.test_info.tc_steps)}"

        # 统计关键词
        keyword_matches = {level: [] for level in technical_keywords}
        for level, keywords in technical_keywords.items():
            for keyword in keywords:
                if keyword in all_text:
                    keyword_matches[level].append(keyword)

        # 计算复杂度分数
        high_count = len(keyword_matches['high'])
        medium_count = len(keyword_matches['medium'])
        low_count = len(keyword_matches['low'])

        complexity_score = high_count * 10 + medium_count * 5 + low_count * 1

        # 确定复杂度级别
        if complexity_score >= 20:
            difficulty_level = "high"
        elif complexity_score >= 10:
            difficulty_level = "medium"
        else:
            difficulty_level = "low"

        return {
            "technical_keywords": keyword_matches,
            "complexity_level": difficulty_level,
            "complexity_score": complexity_score,
            "corpus_id": corpus.rdc_info.rdc_id,
            "keyword_counts": {
                "high": high_count,
                "medium": medium_count,
                "low": low_count
            }
        }

    def get_collection_info_keys(self) -> List[str]:
        return ["technical_keywords", "complexity_level", "complexity_score", "corpus_id", "keyword_counts"]

    def _analyze_code_complexity(self, code_snippets: List) -> int:
        complexity = 0
        complex_patterns = [
            r'class\s+\w+.*{',  # Class definitions
            r'template\s*<',     # Templates
            r'std::\w+',         # STL usage
            r'#include\s*<\w+>', # System includes
            r'virtual\s+\w+',    # Virtual functions
            r'operator\s*\w+',   # Operator overloading
        ]
        
        for snippet in code_snippets:
            content = snippet.content
            for pattern in complex_patterns:
                matches = len(re.findall(pattern, content))
                complexity += matches * 3
            
            lines = content.split('\n')
            if len(lines) > 50:
                complexity += 10
            elif len(lines) > 20:
                complexity += 5
        
        return min(complexity, 30)
    
    def _analyze_steps_complexity(self, steps_text: str) -> int:
        complexity = 0
        complex_indicators = [
            '同时', '并行', '循环', '重复', '条件', '判断', '分支',
            '多个', '批量', '组合', '序列', '依赖', '关联'
        ]

        # 统计复杂性指标出现次数
        for indicator in complex_indicators:
            complexity += steps_text.count(indicator) * 2

        # 根据内容长度判断复杂度
        content_length = len(steps_text.strip())
        if content_length > 1000:
            complexity += 10
        elif content_length > 500:
            complexity += 5

        return min(complexity, 25)


class BusinessComplexityMetric(CorpusMetric):
    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("business_complexity", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        complexity_score = 0
        business_factors = []
        
        business_domains = self.config.get('business_domains', {
            'telecom': ['RAN', '5G', 'LTE', '基站', '小区', '切换', '测量', '信令'],
            'network': ['网络', '协议', '路由', '交换', '传输', '接口'],
            'system': ['系统', '平台', '架构', '模块', '组件', '服务']
        })
        
        all_text = f"{corpus.test_info.test_title} {' '.join(corpus.test_info.tc_steps)}"
        all_tags = (corpus.tag_identification.business_content_scene_tags + 
                   corpus.tag_identification.code_modify_scene_tags)
        tag_text = ' '.join(all_tags)
        
        domain_matches = {}
        for domain, keywords in business_domains.items():
            matches = sum(1 for keyword in keywords if keyword in all_text or keyword in tag_text)
            if matches > 0:
                domain_matches[domain] = matches
                complexity_score += matches * 8
        
        if domain_matches:
            business_factors.append(f"Business domains: {list(domain_matches.keys())}")
        
        scenario_complexity = self._analyze_scenario_complexity(corpus)
        complexity_score += scenario_complexity
        if scenario_complexity > 20:
            business_factors.append("Complex business scenario")
        
        integration_complexity = self._analyze_integration_complexity(corpus)
        complexity_score += integration_complexity
        if integration_complexity > 15:
            business_factors.append("Multi-system integration")
        
        complexity_score = min(100, complexity_score)
        
        if complexity_score >= 60:
            level = EvaluationLevel.INFO
            message = "High business complexity"
            difficulty_level = "High"
        elif complexity_score >= 30:
            level = EvaluationLevel.INFO
            message = "Medium business complexity"
            difficulty_level = "Medium"
        else:
            level = EvaluationLevel.INFO
            message = "Low business complexity"
            difficulty_level = "Low"
        
        return self.create_result(
            score=complexity_score,
            level=level,
            message=message,
            dimension=EvaluationDimension.DIFFICULTY,
            details={
                "difficulty_level": difficulty_level,
                "business_factors": business_factors,
                "domain_matches": domain_matches,
                "scenario_complexity": scenario_complexity,
                "integration_complexity": integration_complexity
            },
            suggestions=[]
        )
    
    def _analyze_scenario_complexity(self, corpus: FTCorpus) -> int:
        complexity = 0
        complex_scenarios = [
            '优化', '异常', '故障', '恢复', '切换', '迁移',
            '负载', '压力', '性能', '容量', '扩展'
        ]
        
        all_text = corpus.test_info.test_title + ' ' + ' '.join(corpus.test_info.tc_steps)
        for scenario in complex_scenarios:
            if scenario in all_text:
                complexity += 5
        
        return min(complexity, 30)
    
    def _analyze_integration_complexity(self, corpus: FTCorpus) -> int:
        complexity = 0
        integration_keywords = [
            '接口', '集成', '对接', '交互', '通信', '协调',
            '同步', '异步', '消息', '事件', '回调'
        ]
        
        all_text = corpus.test_info.test_title + ' ' + ' '.join(corpus.test_info.tc_steps)
        for keyword in integration_keywords:
            if keyword in all_text:
                complexity += 3
        
        if len(corpus.code_snippets) > 3:
            complexity += 10
        
        return min(complexity, 25)


class TestCoverageComplexityMetric(CorpusMetric):
    def __init__(self, weight: float = 0.8, enabled: bool = True):
        super().__init__("test_coverage_complexity", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        coverage_score = 0
        coverage_factors = []
        
        test_types = self._identify_test_types(corpus)
        coverage_score += len(test_types) * 10
        if test_types:
            coverage_factors.append(f"Test types: {', '.join(test_types)}")
        
        test_scenarios = self._count_test_scenarios(corpus)
        coverage_score += min(test_scenarios * 5, 25)
        if test_scenarios > 3:
            coverage_factors.append(f"Multiple scenarios: {test_scenarios}")
        
        edge_cases = self._identify_edge_cases(corpus)
        coverage_score += len(edge_cases) * 8
        if edge_cases:
            coverage_factors.append(f"Edge cases: {len(edge_cases)}")
        
        data_variations = self._analyze_data_variations(corpus)
        coverage_score += data_variations
        if data_variations > 10:
            coverage_factors.append("Multiple data variations")
        
        coverage_score = min(100, coverage_score)
        
        if coverage_score >= 70:
            level = EvaluationLevel.INFO
            message = "Comprehensive test coverage"
            difficulty_level = "High"
        elif coverage_score >= 40:
            level = EvaluationLevel.INFO
            message = "Moderate test coverage"
            difficulty_level = "Medium"
        else:
            level = EvaluationLevel.WARNING
            message = "Limited test coverage"
            difficulty_level = "Low"
        
        return self.create_result(
            score=coverage_score,
            level=level,
            message=message,
            dimension=EvaluationDimension.DIFFICULTY,
            details={
                "difficulty_level": difficulty_level,
                "coverage_factors": coverage_factors,
                "test_types": test_types,
                "test_scenarios": test_scenarios,
                "edge_cases": edge_cases,
                "data_variations": data_variations
            },
            suggestions=[]
        )
    
    def _identify_test_types(self, corpus: FTCorpus) -> List[str]:
        test_types = []
        type_keywords = {
            'functional': ['功能', '业务', '逻辑'],
            'performance': ['性能', '压力', '负载', '响应时间'],
            'integration': ['集成', '接口', '对接'],
            'regression': ['回归', '兼容', '向后'],
            'boundary': ['边界', '极限', '最大', '最小'],
            'negative': ['异常', '错误', '失败', '无效']
        }
        
        all_text = corpus.test_info.test_title + ' ' + ' '.join(corpus.test_info.tc_steps)
        for test_type, keywords in type_keywords.items():
            if any(keyword in all_text for keyword in keywords):
                test_types.append(test_type)
        
        return test_types
    
    def _count_test_scenarios(self, corpus: FTCorpus) -> int:
        scenarios = set()
        scenario_indicators = ['场景', '情况', '条件', '状态', '模式']
        
        for step in corpus.test_info.tc_steps:
            for indicator in scenario_indicators:
                if indicator in step:
                    scenarios.add(step[:20])  # Use first 20 chars as scenario identifier
        
        return len(scenarios)
    
    def _identify_edge_cases(self, corpus: FTCorpus) -> List[str]:
        edge_cases = []
        edge_keywords = [
            '边界', '极限', '最大', '最小', '空', '零', '满',
            '异常', '错误', '失败', '超时', '中断', '断开'
        ]
        
        all_text = corpus.test_info.test_title + ' ' + ' '.join(corpus.test_info.tc_steps)
        for keyword in edge_keywords:
            if keyword in all_text:
                edge_cases.append(keyword)
        
        return list(set(edge_cases))
    
    def _analyze_data_variations(self, corpus: FTCorpus) -> int:
        variations = 0
        variation_indicators = [
            '不同', '多种', '各种', '多个', '批量',
            '随机', '变化', '动态', '可变'
        ]
        
        all_text = corpus.test_info.test_title + ' ' + ' '.join(corpus.test_info.tc_steps)
        for indicator in variation_indicators:
            if indicator in all_text:
                variations += 3
        
        return min(variations, 20)


# 语料集指标
class TechnicalKeywordFrequencyMetric(CollectionMetric):
    """技术关键词频率统计指标"""

    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("technical_keyword_frequency", weight, enabled)

    def calculate(self, collected_info: Dict[str, List[Any]]) -> MetricResult:
        all_keywords = collected_info.get("technical_keywords", [])

        if not all_keywords:
            return self.create_result(
                score=0,
                level=EvaluationLevel.CRITICAL,
                message="无法分析技术关键词：缺少数据",
                dimension=EvaluationDimension.DIFFICULTY,
                details={"error": "No technical keywords data available"}
            )

        # 统计关键词频率
        keyword_frequency = {}
        for keywords_dict in all_keywords:
            for level, keywords in keywords_dict.items():
                for keyword in keywords:
                    if keyword not in keyword_frequency:
                        keyword_frequency[keyword] = 0
                    keyword_frequency[keyword] += 1

        # 分析关键词分布
        most_common_keywords = sorted(keyword_frequency.items(),
                                    key=lambda x: x[1], reverse=True)[:10]

        # 计算多样性分数
        total_unique_keywords = len(keyword_frequency)
        total_corpus = len(all_keywords)
        diversity_score = min(total_unique_keywords / total_corpus * 100, 100) if total_corpus > 0 else 0

        # 确定级别
        if diversity_score >= 80:
            level = EvaluationLevel.INFO
            message = f"技术关键词多样性优秀，共 {total_unique_keywords} 个不同关键词"
        elif diversity_score >= 60:
            level = EvaluationLevel.WARNING
            message = f"技术关键词多样性良好，共 {total_unique_keywords} 个不同关键词"
        else:
            level = EvaluationLevel.CRITICAL
            message = f"技术关键词多样性较低，共 {total_unique_keywords} 个不同关键词"

        return self.create_result(
            score=diversity_score,
            level=level,
            message=message,
            dimension=EvaluationDimension.DIFFICULTY,
            details={
                "total_unique_keywords": total_unique_keywords,
                "total_corpus": total_corpus,
                "most_common_keywords": most_common_keywords,
                "keyword_frequency_distribution": keyword_frequency
            }
        )

    def required_info_keys(self) -> List[str]:
        return ["technical_keywords"]


class DifficultyDistributionMetric(CollectionMetric):
    """难度分布统计指标"""

    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("difficulty_distribution", weight, enabled)

    def calculate(self, collected_info: Dict[str, List[Any]]) -> MetricResult:
        complexity_levels = collected_info.get("complexity_level", [])
        complexity_scores = collected_info.get("complexity_score", [])

        if not complexity_levels:
            return self.create_result(
                score=0,
                level=EvaluationLevel.CRITICAL,
                message="无法分析难度分布：缺少数据",
                dimension=EvaluationDimension.DIFFICULTY,
                details={"error": "No complexity level data available"}
            )

        # 统计难度级别分布
        level_distribution = {'high': 0, 'medium': 0, 'low': 0}
        for level in complexity_levels:
            level_distribution[level] += 1

        total_corpus = len(complexity_levels)
        avg_complexity = sum(complexity_scores) / len(complexity_scores) if complexity_scores else 0

        # 计算分数（基于难度分布的合理性）
        high_ratio = level_distribution['high'] / total_corpus
        medium_ratio = level_distribution['medium'] / total_corpus
        low_ratio = level_distribution['low'] / total_corpus

        # 理想分布：高难度20%，中等难度50%，低难度30%
        ideal_distribution = {'high': 0.2, 'medium': 0.5, 'low': 0.3}
        distribution_score = 100 - (
            abs(high_ratio - ideal_distribution['high']) * 100 +
            abs(medium_ratio - ideal_distribution['medium']) * 100 +
            abs(low_ratio - ideal_distribution['low']) * 100
        ) / 3

        # 确定级别
        if distribution_score >= 80:
            level = EvaluationLevel.INFO
            message = f"难度分布合理，平均复杂度 {avg_complexity:.1f}"
        elif distribution_score >= 60:
            level = EvaluationLevel.WARNING
            message = f"难度分布基本合理，平均复杂度 {avg_complexity:.1f}"
        else:
            level = EvaluationLevel.CRITICAL
            message = f"难度分布不够合理，平均复杂度 {avg_complexity:.1f}"

        return self.create_result(
            score=distribution_score,
            level=level,
            message=message,
            dimension=EvaluationDimension.DIFFICULTY,
            details={
                "total_corpus": total_corpus,
                "level_distribution": level_distribution,
                "level_ratios": {
                    "high": high_ratio,
                    "medium": medium_ratio,
                    "low": low_ratio
                },
                "average_complexity": avg_complexity,
                "ideal_distribution": ideal_distribution
            }
        )

    def required_info_keys(self) -> List[str]:
        return ["complexity_level", "complexity_score"]


# 统一的难度分级维度
class UnifiedDifficultyDimension(UnifiedDimension):
    """统一的难度分级维度，支持单语料和语料集指标"""

    def __init__(self):
        super().__init__(EvaluationDimension.DIFFICULTY)

        # 注册单语料指标
        self.register_corpus_metric(TechnicalComplexityMetric())
        self.register_corpus_metric(BusinessComplexityMetric())
        self.register_corpus_metric(TestCoverageComplexityMetric())

        # 注册语料集指标
        self.register_collection_metric(TechnicalKeywordFrequencyMetric())
        self.register_collection_metric(DifficultyDistributionMetric())


# 保持向后兼容的DifficultyDimension
class DifficultyDimension(BaseDimension):
    def __init__(self):
        super().__init__(EvaluationDimension.DIFFICULTY)
        self.register_metric(TechnicalComplexityMetric())
        self.register_metric(BusinessComplexityMetric())
        self.register_metric(TestCoverageComplexityMetric())
