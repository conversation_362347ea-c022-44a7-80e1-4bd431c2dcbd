import argparse
from pathlib import Path
import json
from ..models.evaluation_models import EvaluationDimension
from ..evaluators.dimension_framework import UnifiedEvaluator
from ..evaluators.completeness_dimension import UnifiedCompletenessDimension
from ..evaluators.correctness_dimension import UnifiedCorrectnessDimension
from ..evaluators.difficulty_dimension import UnifiedDifficultyDimension
from ..parsers.corpus_parser import CorpusParser

def main():
    parser = argparse.ArgumentParser(description="FT Corpus Quality Evaluation Tool - Unified Metrics System")
    parser.add_argument("corpus_dir", type=str, nargs='?', help="Directory containing corpus files")
    parser.add_argument("-o", "--output", type=str, help="Output directory for results")
    parser.add_argument("-c", "--config", type=str,
                       help="Configuration file path (JSON format, see config/config.json for example)")
    parser.add_argument("-p", "--pattern", type=str, default="*.md", help="File pattern to match")

    # 维度选择
    parser.add_argument("--dimensions", type=str, nargs="+",
                       choices=["completeness", "correctness", "difficulty", "all"],
                       default=["all"],
                       help="Evaluation dimensions to run (overrides config file settings)")

    # 指标管理
    parser.add_argument("--list-metrics", action="store_true",
                       help="List all available metrics for each dimension")
    parser.add_argument("--disable-corpus-metric", type=str, nargs=2, metavar=("DIMENSION", "METRIC"),
                       action="append", help="Disable specific corpus metric")
    parser.add_argument("--disable-collection-metric", type=str, nargs=2, metavar=("DIMENSION", "METRIC"),
                       action="append", help="Disable specific collection metric")
    parser.add_argument("--enable-corpus-metric", type=str, nargs=2, metavar=("DIMENSION", "METRIC"),
                       action="append", help="Enable specific corpus metric")
    parser.add_argument("--enable-collection-metric", type=str, nargs=2, metavar=("DIMENSION", "METRIC"),
                       action="append", help="Enable specific collection metric")
    
    args = parser.parse_args()

    if args.list_metrics:
        list_unified_metrics()
        return 0

    if not args.corpus_dir:
        print("Error: corpus_dir is required for evaluation operations")
        parser.print_help()
        return 1

    corpus_dir = Path(args.corpus_dir)
    if not corpus_dir.exists():
        print(f"Error: Corpus directory {corpus_dir} does not exist")
        return 1

    output_dir = Path(args.output) if args.output else corpus_dir / "evaluation_results"

    return run_unified_evaluation(args, corpus_dir, output_dir)


def run_unified_evaluation(args, corpus_dir: Path, output_dir: Path):
    print("🚀 FT语料质量评估")
    print(f"📁 语料目录: {corpus_dir}")
    print(f"📄 文件模式: {args.pattern}")
    print(f"📊 输出目录: {output_dir}")

    evaluator = UnifiedEvaluator()

    if args.config:
        config_path = Path(args.config)
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                evaluator.set_config(config.get('dimensions', {}))
                print(f"✅ 已加载配置文件: {config_path}")
            except Exception as e:
                print(f"⚠️  配置文件加载失败: {e}")
        else:
            print(f"⚠️  配置文件不存在: {config_path}")

    # 注册维度（优先使用命令行参数，其次使用配置文件）
    dimensions_to_register = []

    # 检查配置文件中是否有维度配置
    config_dimensions = set()
    if hasattr(evaluator, 'config') and evaluator.config:
        for dim_name, dim_config in evaluator.config.items():
            if dim_config.get('enabled', True):
                config_dimensions.add(dim_name)

    # 如果命令行指定了维度，使用命令行参数；否则使用配置文件
    if args.dimensions != ["all"] or not config_dimensions:
        # 使用命令行参数
        if "all" in args.dimensions or "completeness" in args.dimensions:
            dimensions_to_register.append(("completeness", UnifiedCompletenessDimension()))
        if "all" in args.dimensions or "correctness" in args.dimensions:
            dimensions_to_register.append(("correctness", UnifiedCorrectnessDimension()))
        if "all" in args.dimensions or "difficulty" in args.dimensions:
            dimensions_to_register.append(("difficulty", UnifiedDifficultyDimension()))
    else:
        # 使用配置文件
        if "completeness" in config_dimensions:
            dimensions_to_register.append(("completeness", UnifiedCompletenessDimension()))
        if "correctness" in config_dimensions:
            dimensions_to_register.append(("correctness", UnifiedCorrectnessDimension()))
        if "difficulty" in config_dimensions:
            dimensions_to_register.append(("difficulty", UnifiedDifficultyDimension()))

    for dim_name, dimension in dimensions_to_register:
        evaluator.register_dimension(dimension)
        print(f"✅ 已注册 {dim_name} 维度")

    handle_unified_metric_config(evaluator, args)

    parser = CorpusParser()
    corpus_files = list(corpus_dir.glob(args.pattern))

    if not corpus_files:
        print(f"❌ 在 {corpus_dir} 中未找到匹配 {args.pattern} 的文件")
        return 1

    try:
        print("⚡ 执行完整评估（单语料 + 语料集指标）")
        results = evaluator.evaluate_corpus_collection(corpus_files, parser)

        output_dir.mkdir(parents=True, exist_ok=True)
        save_unified_results(results, output_dir)

        display_unified_results(results)

        print(f"\n💾 结果已保存到: {output_dir}")
        return 0

    except Exception as e:
        print(f"❌ 评估过程中发生错误: {e}")
        return 1


def list_unified_metrics():
    """列出统一指标体系中的所有指标"""
    print("🚀 统一指标体系 - 可用指标:")
    print("=" * 60)

    # 创建临时评估器来获取指标信息
    evaluator = UnifiedEvaluator()
    evaluator.register_dimension(UnifiedCompletenessDimension())
    evaluator.register_dimension(UnifiedCorrectnessDimension())
    evaluator.register_dimension(UnifiedDifficultyDimension())

    dimension_names = {
        "completeness": "📋 完整性维度",
        "correctness": "✅ 正确性维度",
        "difficulty": "🎯 难度分级维度"
    }

    for dim_type, dimension in evaluator.dimensions.items():
        display_name = dimension_names.get(dim_type.value, dim_type.value)
        print(f"\n{display_name}:")

        # 单语料指标
        print("  📝 单语料指标:")
        for metric in dimension.corpus_metrics:
            status = "✅ 启用" if metric.enabled else "❌ 禁用"
            print(f"    • {metric.name}: {status}")

        # 语料集指标
        print("  📊 语料集指标:")
        for metric in dimension.collection_metrics:
            status = "✅ 启用" if metric.enabled else "❌ 禁用"
            print(f"    • {metric.name}: {status}")





def handle_unified_metric_config(evaluator: UnifiedEvaluator, args):
    # 处理单语料指标配置
    if args.disable_corpus_metric:
        for dimension_name, metric_name in args.disable_corpus_metric:
            try:
                dimension_enum = EvaluationDimension(dimension_name)
                if dimension_enum in evaluator.dimensions:
                    evaluator.dimensions[dimension_enum].disable_corpus_metric(metric_name)
                    print(f"❌ 已禁用单语料指标: {dimension_name}.{metric_name}")
                else:
                    print(f"⚠️  未知维度: {dimension_name}")
            except ValueError:
                print(f"⚠️  未知维度: {dimension_name}")

    if args.enable_corpus_metric:
        for dimension_name, metric_name in args.enable_corpus_metric:
            try:
                dimension_enum = EvaluationDimension(dimension_name)
                if dimension_enum in evaluator.dimensions:
                    evaluator.dimensions[dimension_enum].enable_corpus_metric(metric_name)
                    print(f"✅ 已启用单语料指标: {dimension_name}.{metric_name}")
                else:
                    print(f"⚠️  未知维度: {dimension_name}")
            except ValueError:
                print(f"⚠️  未知维度: {dimension_name}")

    # 处理语料集指标配置
    if args.disable_collection_metric:
        for dimension_name, metric_name in args.disable_collection_metric:
            try:
                dimension_enum = EvaluationDimension(dimension_name)
                if dimension_enum in evaluator.dimensions:
                    evaluator.dimensions[dimension_enum].disable_collection_metric(metric_name)
                    print(f"❌ 已禁用语料集指标: {dimension_name}.{metric_name}")
                else:
                    print(f"⚠️  未知维度: {dimension_name}")
            except ValueError:
                print(f"⚠️  未知维度: {dimension_name}")

    if args.enable_collection_metric:
        for dimension_name, metric_name in args.enable_collection_metric:
            try:
                dimension_enum = EvaluationDimension(dimension_name)
                if dimension_enum in evaluator.dimensions:
                    evaluator.dimensions[dimension_enum].enable_collection_metric(metric_name)
                    print(f"✅ 已启用语料集指标: {dimension_name}.{metric_name}")
                else:
                    print(f"⚠️  未知维度: {dimension_name}")
            except ValueError:
                print(f"⚠️  未知维度: {dimension_name}")


def save_unified_results(results: dict, output_dir: Path):
    """保存统一指标体系的评估结果"""

    # 转换结果为可序列化格式
    def convert_to_serializable(obj):
        if hasattr(obj, 'to_dict'):
            return obj.to_dict()
        elif isinstance(obj, dict):
            return {k: convert_to_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_to_serializable(item) for item in obj]
        elif hasattr(obj, 'value'):  # 枚举类型
            return obj.value
        else:
            return obj

    serializable_results = convert_to_serializable(results)

    # 保存主要结果文件
    main_file = output_dir / "unified_evaluation_results.json"
    with open(main_file, "w", encoding="utf-8") as f:
        json.dump(serializable_results, f, ensure_ascii=False, indent=2)

    # 如果包含语料集结果，单独保存
    if 'collection_results' in results:
        collection_file = output_dir / "collection_statistics.json"
        with open(collection_file, "w", encoding="utf-8") as f:
            json.dump(convert_to_serializable(results['collection_results']),
                     f, ensure_ascii=False, indent=2)

    # 如果包含单语料结果，保存摘要
    if 'corpus_results' in results:
        summary_file = output_dir / "corpus_evaluation_summary.json"
        summary = {
            'total_corpus': len(results['corpus_results']),
            'summary': results.get('summary', {}),
            'first_few_results': results['corpus_results'][:3]  # 只保存前几个作为示例
        }
        with open(summary_file, "w", encoding="utf-8") as f:
            json.dump(convert_to_serializable(summary), f, ensure_ascii=False, indent=2)


def display_unified_results(results: dict):
    """显示统一指标体系的评估结果"""

    print("\n" + "=" * 60)
    print("🎉 统一指标体系评估结果")
    print("=" * 60)

    # 显示基本信息
    if 'summary' in results:
        summary = results['summary']
        print(f"📊 总语料数: {summary.get('total_corpus_count', 0)}")
        print(f"📋 评估维度: {', '.join([dim.value for dim in summary.get('dimensions_evaluated', [])])}")

    # 显示单语料结果摘要
    if 'corpus_results' in results:
        corpus_results = results['corpus_results']
        print("\n📝 单语料评估结果 (显示前3个):")

        for i, corpus_result in enumerate(corpus_results[:3], 1):
            print(f"\n  语料 {i}: {corpus_result['corpus_id']}")

            for dim_name, dim_results in corpus_result['results'].items():
                scores = [result.score for result in dim_results.values()]
                avg_score = sum(scores) / len(scores) if scores else 0
                print(f"    {dim_name}: {avg_score:.1f}分 ({len(dim_results)}个指标)")

        if len(corpus_results) > 3:
            print(f"\n  ... 还有 {len(corpus_results) - 3} 个语料的结果")

    # 显示语料集统计结果
    if 'collection_results' in results:
        collection_results = results['collection_results']
        print("\n📊 语料集统计结果:")

        for dim_name, dim_results in collection_results.items():
            print(f"\n  📋 {dim_name} 维度:")
            for metric_name, metric_result in dim_results.items():
                level_emoji = {
                    'info': '✅',
                    'warning': '⚠️',
                    'critical': '❌'
                }.get(metric_result.level.value, '❓')

                print(f"    {level_emoji} {metric_name}: {metric_result.score:.1f}分")
                print(f"       {metric_result.message}")

                # 显示关键统计信息
                if hasattr(metric_result, 'details') and metric_result.details:
                    details = metric_result.details
                    if 'most_missing_fields' in details:
                        missing = details['most_missing_fields'][:3]
                        if missing:
                            print(f"       最常缺失: {', '.join([f'{field}({count})' for field, count in missing])}")

                    if 'most_common_keywords' in details:
                        keywords = details['most_common_keywords'][:3]
                        if keywords:
                            print(f"       常见关键词: {', '.join([f'{kw}({count})' for kw, count in keywords])}")

                    if 'level_distribution' in details:
                        dist = details['level_distribution']
                        print(f"       分布: 高{dist.get('high', 0)} 中{dist.get('medium', 0)} 低{dist.get('low', 0)}")


if __name__ == "__main__":
    exit(main())