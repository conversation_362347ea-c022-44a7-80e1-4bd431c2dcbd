{"dimensions": {"completeness": {"enabled": true, "corpus_metrics": {"required_fields_check": {"enabled": true, "weight": 1.0}, "content_structure_check": {"enabled": true, "weight": 1.0}, "test_steps_completeness": {"enabled": true, "weight": 1.0}, "code_snippets_completeness": {"enabled": true, "weight": 0.8}}, "collection_metrics": {"field_coverage_analysis": {"enabled": true, "weight": 1.0}, "content_diversity_analysis": {"enabled": true, "weight": 1.0}}}, "correctness": {"enabled": true, "corpus_metrics": {"format_validation": {"enabled": true, "weight": 1.0}, "logical_consistency_check": {"enabled": true, "weight": 1.0}, "data_validity_check": {"enabled": true, "weight": 1.0}}, "collection_metrics": {"format_consistency_analysis": {"enabled": true, "weight": 1.0}, "data_quality_distribution": {"enabled": true, "weight": 1.0}}}, "difficulty": {"enabled": true, "corpus_metrics": {"technical_complexity_assessment": {"enabled": true, "weight": 1.0}, "business_complexity_assessment": {"enabled": true, "weight": 1.0}, "test_coverage_complexity": {"enabled": true, "weight": 0.8}}, "collection_metrics": {"difficulty_distribution_analysis": {"enabled": true, "weight": 1.0}, "complexity_balance_check": {"enabled": true, "weight": 1.0}}}}}