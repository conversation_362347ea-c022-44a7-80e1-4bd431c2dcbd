from pathlib import Path
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import json
from ..parsers.corpus_parser import CorpusParser
from ..core.evaluator_manager import EvaluatorManager
from ..models.evaluation_models import (
    CorpusEvaluationReport, EvaluationLevel, EvaluationDimension
)


class EvaluationJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, EvaluationLevel):
            return obj.value
        elif isinstance(obj, EvaluationDimension):
            return obj.value
        return super().default(obj)

class BatchProcessor:
    def __init__(self, evaluator_manager: EvaluatorManager, max_workers: int = 4):
        self.evaluator_manager = evaluator_manager
        self.parser = CorpusParser()
        self.max_workers = max_workers
    
    def process_directory(self, corpus_dir: Path, 
                         file_pattern: str = "*.md",
                         output_dir: Optional[Path] = None) -> Dict[str, Any]:
        
        corpus_files = list(corpus_dir.glob(file_pattern))
        if not corpus_files:
            return {"error": f"No files found matching pattern {file_pattern} in {corpus_dir}"}
        
        reports = []
        failed_files = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_file = {
                executor.submit(self._process_single_file, file_path): file_path
                for file_path in corpus_files
            }
            
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    report = future.result()
                    if report:
                        reports.append(report)
                except Exception as e:
                    failed_files.append({
                        "file_path": str(file_path),
                        "error": str(e)
                    })
        
        summary = self._generate_summary(reports)
        
        result = {
            "summary": summary,
            "reports": [report.to_dict() for report in reports],
            "failed_files": failed_files,
            "total_processed": len(reports),
            "total_failed": len(failed_files)
        }
        
        if output_dir:
            self._save_results(result, output_dir)
        
        return result
    
    def _process_single_file(self, file_path: Path) -> Optional[CorpusEvaluationReport]:
        try:
            corpus = self.parser.parse_file(file_path)
            report = self.evaluator_manager.evaluate_corpus(corpus)
            return report
        except Exception as e:
            print(f"Failed to process {file_path}: {e}")
            return None
    
    def _generate_summary(self, reports: List[CorpusEvaluationReport]) -> Dict[str, Any]:
        if not reports:
            return {"error": "No reports to summarize"}
        
        total_score = sum(report.overall_score for report in reports)
        avg_score = total_score / len(reports)
        
        score_distribution = {
            "excellent": len([r for r in reports if r.overall_score >= 90]),
            "good": len([r for r in reports if 80 <= r.overall_score < 90]),
            "fair": len([r for r in reports if 60 <= r.overall_score < 80]),
            "poor": len([r for r in reports if r.overall_score < 60])
        }
        
        issue_counts = {}
        for report in reports:
            for result in report.evaluation_results:
                if result.level.value not in issue_counts:
                    issue_counts[result.level.value] = 0
                issue_counts[result.level.value] += 1
        
        top_issues = self._get_top_issues(reports)
        
        return {
            "total_files": len(reports),
            "average_score": round(avg_score, 2),
            "score_distribution": score_distribution,
            "issue_counts": issue_counts,
            "top_issues": top_issues
        }
    
    def _get_top_issues(self, reports: List[CorpusEvaluationReport], top_n: int = 5) -> List[Dict[str, Any]]:
        issue_frequency = {}
        
        for report in reports:
            for result in report.evaluation_results:
                if result.level.value != EvaluationLevel.INFO.value:
                    key = f"{result.evaluator_name}: {result.message}"
                    if key not in issue_frequency:
                        issue_frequency[key] = 0
                    issue_frequency[key] += 1
        
        sorted_issues = sorted(issue_frequency.items(), key=lambda x: x[1], reverse=True)
        
        return [
            {"issue": issue, "frequency": freq}
            for issue, freq in sorted_issues[:top_n]
        ]
    
    def _save_results(self, result: Dict[str, Any], output_dir: Path):
        output_dir.mkdir(parents=True, exist_ok=True)

        with open(output_dir / "evaluation_results.json", "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False, cls=EvaluationJSONEncoder)
        
        self._generate_html_report(result, output_dir / "evaluation_report.html")
    
    def _generate_html_report(self, result: Dict[str, Any], output_path: Path):
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>FT Corpus Evaluation Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .summary {{ background: #f5f5f5; padding: 15px; border-radius: 5px; }}
                .score-excellent {{ color: #28a745; }}
                .score-good {{ color: #17a2b8; }}
                .score-fair {{ color: #ffc107; }}
                .score-poor {{ color: #dc3545; }}
                table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <h1>FT Corpus Evaluation Report</h1>
            <div class="summary">
                <h2>Summary</h2>
                <p>Total Files: {total_files}</p>
                <p>Average Score: {average_score}</p>
                <p>Score Distribution:</p>
                <ul>
                    <li class="score-excellent">Excellent (90-100): {excellent}</li>
                    <li class="score-good">Good (80-89): {good}</li>
                    <li class="score-fair">Fair (60-79): {fair}</li>
                    <li class="score-poor">Poor (0-59): {poor}</li>
                </ul>
            </div>
            
            <h2>Top Issues</h2>
            <table>
                <tr><th>Issue</th><th>Frequency</th></tr>
                {top_issues_rows}
            </table>
        </body>
        </html>
        """
        
        summary = result["summary"]
        top_issues_rows = ""
        for issue in summary.get("top_issues", []):
            top_issues_rows += f"<tr><td>{issue['issue']}</td><td>{issue['frequency']}</td></tr>"
        
        html_content = html_template.format(
            total_files=summary.get("total_files", 0),
            average_score=summary.get("average_score", 0),
            excellent=summary.get("score_distribution", {}).get("excellent", 0),
            good=summary.get("score_distribution", {}).get("good", 0),
            fair=summary.get("score_distribution", {}).get("fair", 0),
            poor=summary.get("score_distribution", {}).get("poor", 0),
            top_issues_rows=top_issues_rows
        )

        output_path.write_text(html_content, encoding="utf-8")

    def process_directory_by_dimension(self, corpus_dir: Path,
                                     dimension: EvaluationDimension,
                                     file_pattern: str = "*.md",
                                     output_dir: Optional[Path] = None) -> Dict[str, Any]:
        corpus_files = list(corpus_dir.glob(file_pattern))
        if not corpus_files:
            return {"error": f"No files found matching pattern {file_pattern} in {corpus_dir}"}

        dimension_results = []
        failed_files = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_file = {
                executor.submit(self._process_single_file_dimension, file_path, dimension): file_path
                for file_path in corpus_files
            }

            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    result = future.result()
                    if result:
                        dimension_results.append(result)
                except Exception as e:
                    failed_files.append({
                        "file_path": str(file_path),
                        "error": str(e)
                    })

        summary = self._generate_dimension_summary(dimension_results, dimension)

        result = {
            "dimension": dimension.value,
            "summary": summary,
            "results": dimension_results,
            "failed_files": failed_files,
            "total_processed": len(dimension_results),
            "total_failed": len(failed_files)
        }

        if output_dir:
            self._save_dimension_results(result, output_dir, dimension)

        return result

    def _process_single_file_dimension(self, file_path: Path, dimension: EvaluationDimension):
        try:
            corpus = self.parser.parse_file(file_path)
            dimension_result = self.evaluator_manager.evaluate_by_dimension(corpus, dimension)
            if dimension_result:
                return {
                    "file_path": str(file_path),
                    "dimension": dimension.value,
                    "overall_score": dimension_result.overall_score,
                    "weighted_score": dimension_result.weighted_score,
                    "metric_results": [result.to_dict() for result in dimension_result.metric_results],
                    "enabled_metrics": dimension_result.enabled_metrics
                }
        except Exception as e:
            print(f"Failed to process {file_path} for dimension {dimension.value}: {e}")
            return None

    def _generate_dimension_summary(self, results: List[Dict[str, Any]],
                                  dimension: EvaluationDimension) -> Dict[str, Any]:
        if not results:
            return {"error": "No results to summarize"}

        scores = [result["overall_score"] for result in results]
        avg_score = sum(scores) / len(scores)

        score_distribution = {
            "excellent": len([s for s in scores if s >= 90]),
            "good": len([s for s in scores if 80 <= s < 90]),
            "fair": len([s for s in scores if 60 <= s < 80]),
            "poor": len([s for s in scores if s < 60])
        }

        metric_issues = {}
        for result in results:
            for metric_result in result["metric_results"]:
                if metric_result["level"] in ["critical", "warning"]:
                    metric_name = metric_result["metric_name"]
                    if metric_name not in metric_issues:
                        metric_issues[metric_name] = 0
                    metric_issues[metric_name] += 1

        top_metric_issues = sorted(metric_issues.items(), key=lambda x: x[1], reverse=True)[:5]

        return {
            "dimension": dimension.value,
            "total_files": len(results),
            "average_score": round(avg_score, 2),
            "score_distribution": score_distribution,
            "top_metric_issues": [
                {"metric": metric, "frequency": freq}
                for metric, freq in top_metric_issues
            ]
        }

    def _save_dimension_results(self, result: Dict[str, Any], output_dir: Path,
                              dimension: EvaluationDimension):
        output_dir.mkdir(parents=True, exist_ok=True)

        json_file = output_dir / f"dimension_{dimension.value}_results.json"
        with open(json_file, "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

    def get_dimension_metrics_status(self) -> Dict[str, Dict[str, bool]]:
        return self.evaluator_manager.get_dimension_metrics_status()

    def enable_dimension_metric(self, dimension: EvaluationDimension, metric_name: str):
        self.evaluator_manager.enable_dimension_metric(dimension, metric_name)

    def process_directory_with_collection_stats(self, corpus_dir: Path,
                                               file_pattern: str = "*.md",
                                               output_dir: Optional[Path] = None) -> Dict[str, Any]:
        """处理目录并生成整体统计信息"""

        # 开始收集
        self.evaluator_manager.start_corpus_collection()

        try:
            # 处理所有文件（这会自动收集信息）
            regular_results = self.process_directory(corpus_dir, file_pattern, None)  # 不保存常规结果

            # 获取整体统计
            collection_stats = self.evaluator_manager.get_all_dimensions_collection_statistics()

            # 合并结果
            result = {
                **regular_results,
                'collection_statistics': collection_stats
            }

            if output_dir:
                self._save_collection_results(result, output_dir)

            return result

        finally:
            # 确保停止收集
            self.evaluator_manager.stop_corpus_collection()

    def process_dimension_with_collection_stats(self, corpus_dir: Path,
                                              dimension: EvaluationDimension,
                                              file_pattern: str = "*.md",
                                              output_dir: Optional[Path] = None) -> Dict[str, Any]:
        """处理特定维度并生成整体统计信息"""

        # 开始收集
        self.evaluator_manager.start_corpus_collection()

        try:
            # 处理所有文件（这会自动收集信息）
            regular_results = self.process_directory_by_dimension(
                corpus_dir, dimension, file_pattern, None
            )

            # 获取特定维度的整体统计
            dimension_stats = self.evaluator_manager.get_dimension_collection_statistics(dimension)

            # 合并结果
            result = {
                **regular_results,
                'dimension_collection_statistics': dimension_stats
            }

            if output_dir:
                self._save_dimension_collection_results(result, output_dir, dimension)

            return result

        finally:
            # 确保停止收集
            self.evaluator_manager.stop_corpus_collection()

    def _save_collection_results(self, result: Dict[str, Any], output_dir: Path):
        """保存包含整体统计的结果"""
        output_dir.mkdir(parents=True, exist_ok=True)

        # 保存完整结果
        json_file = output_dir / "evaluation_results_with_collection_stats.json"
        with open(json_file, "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2, cls=EvaluationJSONEncoder)

        # 单独保存整体统计
        stats_file = output_dir / "collection_statistics.json"
        with open(stats_file, "w", encoding="utf-8") as f:
            json.dump(result['collection_statistics'], f, ensure_ascii=False, indent=2, cls=EvaluationJSONEncoder)

    def _save_dimension_collection_results(self, result: Dict[str, Any],
                                         output_dir: Path, dimension: EvaluationDimension):
        """保存维度特定的整体统计结果"""
        output_dir.mkdir(parents=True, exist_ok=True)

        # 保存完整结果
        json_file = output_dir / f"dimension_{dimension.value}_with_collection_stats.json"
        with open(json_file, "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2, cls=EvaluationJSONEncoder)

        # 单独保存维度统计
        stats_file = output_dir / f"dimension_{dimension.value}_collection_statistics.json"
        with open(stats_file, "w", encoding="utf-8") as f:
            json.dump(result['dimension_collection_statistics'], f, ensure_ascii=False, indent=2, cls=EvaluationJSONEncoder)

    def disable_dimension_metric(self, dimension: EvaluationDimension, metric_name: str):
        self.evaluator_manager.disable_dimension_metric(dimension, metric_name)