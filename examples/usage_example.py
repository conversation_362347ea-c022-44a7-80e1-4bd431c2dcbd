from pathlib import Path
import sys

sys.path.append(str(Path(__file__).parent.parent))

from ft_corpus_evaluator.core.evaluator_manager import EvaluatorManager
from ft_corpus_evaluator.parsers.markdown_corpus_parser import MarkdownCorpusParser


def example_single_file_evaluation():
    print("=== 单文件维度评估示例 ===")

    evaluator_manager = EvaluatorManager()
    
    corpus_file = Path("corpus/测试用例 RAN-6612580_ 低速迁出测量场景，存在gumai惩罚的邻区，异系统测量上报无输出.md")
    if not corpus_file.exists():
        print(f"示例文件 {corpus_file} 不存在，跳过单文件评估")
        return
    
    # parser = CorpusParser()
    # corpus = parser.parse_file(corpus_file)
    parser = MarkdownCorpusParser()

    corpus = parser.parse_file(corpus_file)

    
    report = evaluator_manager.evaluate_corpus(corpus)
    
    print(f"文件: {report.file_path}")
    print(f"综合评分: {report.overall_score:.2f}")
    print(f"评估结果数量: {len(report.evaluation_results)}")
    
    for result in report.evaluation_results:
        if result.dimension:
            print(f"\n维度: {result.dimension.value}")
            print(f"  评分: {result.score:.2f}")
            print(f"  级别: {result.level.value}")
            print(f"  消息: {result.message}")
            if result.suggestions:
                print(f"  建议: {result.suggestions[:2]}")


def main():
    print("维度评估框架使用示例")
    print("=" * 50)
    
    try:
        example_single_file_evaluation()

        print("\n" + "=" * 50)
        print("所有示例执行完成！")
        
    except Exception as e:
        print(f"示例执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
