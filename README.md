# FT语料质量评估工具

## 🚀 快速开始

### 基本使用

```bash
# 评估语料目录中的所有文件
python -m ft_corpus_evaluator.cli.main corpus

# 查看可用指标
python -m ft_corpus_evaluator.cli.main --list-metrics

# 指定输出目录
python -m ft_corpus_evaluator.cli.main corpus -o results/
```

## 💡 核心特性

- **统一指标体系**：单语料指标 + 语料集指标
- **内存优化**：逐个处理语料文件，支持大型语料集
- **简洁易用**：一个命令完成全面评估
- **灵活配置**：可控制指标启用/禁用
- **丰富输出**：个体质量 + 整体模式分析

## 📋 可用指标

### 📋 完整性维度
- **单语料指标**：必填字段、内容结构、测试步骤、代码片段
- **语料集指标**：字段缺失分布、完整性模式分析

### ✅ 正确性维度
- **单语料指标**：格式正确性、逻辑一致性、数据有效性
- **语料集指标**：格式问题分布、一致性模式分析

### 🎯 难度分级维度
- **单语料指标**：技术复杂度、业务复杂度、测试覆盖复杂度
- **语料集指标**：技术关键词频率、难度分布分析

## 🎯 常用命令

```bash
# 基本评估
python -m ft_corpus_evaluator.cli.main corpus

# 查看指标
python -m ft_corpus_evaluator.cli.main --list-metrics

# 特定维度
python -m ft_corpus_evaluator.cli.main corpus --dimension-only completeness

# 自定义输出
python -m ft_corpus_evaluator.cli.main corpus -o results/

# 禁用指标
python -m ft_corpus_evaluator.cli.main corpus \
  --disable-corpus-metric completeness required_fields
```